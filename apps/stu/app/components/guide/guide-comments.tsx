"use client";

import { useCommentsList } from "@/app/models";
import { ApiComment, useSubCommentsList } from "@/app/models/comments";
import { getRelativeTime } from "@/app/utils/getRelativeTime";
import DeleteIcon from "@/public/icons/delete.svg";
import DownArrowIcon from "@/public/icons/DownArrow.svg";
import CommentIcon from "@/public/icons/ic_tab_growth_default.svg";
import LikeIcon from "@/public/icons/like.svg";
import TeacherRecommendIcon from "@/public/icons/TeacherRecommend.svg";
import { useComputed, useSignal } from "@preact-signals/safe-react";
import {
  useCallback,
  useEffect,
  useMemo,
  useRef,
} from "@preact-signals/safe-react/react";
import { cn } from "@repo/ui/lib/utils";
import { AnimatePresence, motion } from "motion/react";
import Image from "next/image";
import { FC, useLayoutEffect } from "react";
import { InfinityScroll } from "../common";

// 评论输入框组件
const CommentInput: FC<{
  inputValue: string;
  onClick: () => void;
}> = ({ inputValue, onClick }) => {
  return (
    <div
      className="sticky bottom-0 flex-[0_0_auto] bg-white px-4 py-3 shadow-[0_-1px_0px_0px_rgba(0,0,0,0.05)]"
      onClick={onClick}
    >
      <div className="bg-fill-gray flex items-center gap-0.5 rounded-full px-3 py-2">
        <CommentIcon className="text-text-5 size-5 flex-[0_0_auto]" />
        <div
          className={cn(
            inputValue ? "text-text-2" : "text-text-5",
            "flex-1 overflow-hidden text-ellipsis whitespace-nowrap text-[13px] font-medium"
          )}
        >
          {inputValue || "发表你的观点"}
        </div>
      </div>
    </div>
  );
};

// 单个评论项组件
const CommentItem = ({
  objId,
  comment,
  isReply,
  onLike,
  onReply,
  onDelete,
  hadShownRedirectComment,
  redirectCommentId,
  redirectCommentRootId,
  onRedirectedAction,
}: {
  objId?: number;
  comment: ApiComment;
  isReply?: boolean;
  onLike: (id: number) => Promise<unknown>;
  onReply: (id: number, commentContent: string) => Promise<unknown>;
  onDelete: (id: number) => Promise<unknown>;
  hadShownRedirectComment: boolean;
  redirectCommentId?: string;
  redirectCommentRootId?: string;
  onRedirectedAction?: () => void;
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const isOverLines = useSignal(false);
  const isExpanded = useSignal(false);
  const isShowExpandIcon = useComputed(
    () => !isExpanded.value && isOverLines.value
  );
  const isExpandedSubComments = useSignal(false);
  const showOffset = useSignal(0);

  const { data, refresh, loadMore, hasMore } = useSubCommentsList(
    !isReply && objId && comment.repliesCount > 0 && isExpandedSubComments.value
      ? {
          objId,
          objType: 100,
          pageSize: 50,
          commentRootId: comment.commentId,
        }
      : null
  );

  const list = useMemo(
    () => data?.list.slice(0, showOffset.value) ?? [],
    [data?.list, showOffset.value]
  );
  const calculatedHasMore = hasMore || (data?.list ?? []).length > list.length;

  const loadMoreComments = useCallback(async () => {
    if (!calculatedHasMore) return;
    if (hasMore) await loadMore();
    if (showOffset.value === 0) showOffset.value = 3;
    else showOffset.value += 8;
  }, [calculatedHasMore, hasMore, loadMore, showOffset]);

  useLayoutEffect(() => {
    if (!contentRef.current) return;
    isOverLines.value =
      contentRef.current.scrollHeight - contentRef.current.clientHeight > 10;
  }, [isOverLines]);

  useEffect(() => {
    if (hadShownRedirectComment) return;
    if (
      comment.commentId === Number(redirectCommentRootId) &&
      !isExpandedSubComments.value &&
      comment.repliesCount > 0
    ) {
      isExpandedSubComments.value = true;
      if (showOffset.value === 0) showOffset.value = 3;
      return;
    }
    if (
      comment.commentId === Number(redirectCommentRootId) ||
      comment.commentId === Number(redirectCommentId) ||
      list.find(
        (item) =>
          item.commentId === Number(redirectCommentRootId) ||
          item.commentId === Number(redirectCommentId)
      )
    ) {
      ref.current.scrollIntoView({
        block: "center",
        inline: "center",
        behavior: "smooth",
      });
      onRedirectedAction?.();
      return;
    }
    loadMoreComments();
  }, [
    comment.commentId,
    comment.repliesCount,
    hadShownRedirectComment,
    isExpandedSubComments,
    list,
    loadMoreComments,
    onRedirectedAction,
    redirectCommentId,
    redirectCommentRootId,
    showOffset,
  ]);

  return (
    <motion.div
      initial={{ opacity: 0, x: 10 }}
      exit={{ opacity: 0, x: 10 }}
      animate={{ opacity: 1, x: 0 }}
      className={cn(
        "flex w-full flex-col gap-2",
        isReply ? "pl-5 pt-3" : "py-4"
      )}
      ref={ref}
    >
      <div className="flex flex-col gap-3">
        <div className="flex flex-col gap-2">
          <div className="flex justify-between gap-2">
            <div className="flex flex-1 items-center gap-1 overflow-hidden text-[13px]">
              <div className="size-4 flex-[0_0_auto] overflow-hidden rounded-full">
                <Image
                  src={comment.userAvatar}
                  width={16}
                  height={16}
                  alt={comment.userName}
                />
              </div>
              <div className="text-text-4 flex-[0_0_auto]">
                {comment.userName}
              </div>
              {(comment.schoolName || comment.className) && !comment.isMe && (
                <div className="text-text-5 flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
                  {" "}
                  · {comment.className || comment.schoolName}
                </div>
              )}
              {Boolean(comment.isMe) && (
                <div className="text-text-4 border-1 border-fill-3 rounded-full px-1.5 text-[10px]">
                  我的
                </div>
              )}
            </div>
            {Boolean(comment.isTeacherRecommended) && (
              <TeacherRecommendIcon className="flex-[0_0_auto]" />
            )}
          </div>
          <div
            ref={contentRef}
            className={cn(
              "text-text-2 text-[15px] leading-tight",
              !isExpanded.value && "line-clamp-5",
              isShowExpandIcon.value &&
                "before:float-right before:h-[5em] before:w-0"
            )}
          >
            {isShowExpandIcon.value && (
              <DownArrowIcon
                className="text-dim-blue float-right clear-both"
                onClick={() => {
                  isExpanded.value = true;
                }}
              />
            )}
            {comment.replyToUserName && (
              <>
                回复
                <span className="text-text-5">{comment.replyToUserName}：</span>
              </>
            )}
            {comment.commentContent}
          </div>
        </div>
        <div className="text-text-5 flex items-center justify-between gap-2 text-[13px] font-medium">
          <div>
            {getRelativeTime(comment.createdAt * 1000 || comment.publishedAt)}
          </div>
          <div className="flex items-center gap-1">
            {Boolean(comment.isMe) && (
              <DeleteIcon
                onClick={() => onDelete(comment.commentId)}
                className="size-5"
              />
            )}
            <CommentIcon
              onClick={async () => {
                await onReply(comment.commentId, comment.commentContent);
                if (!isReply) {
                  isExpandedSubComments.value = true;
                  if (showOffset.value === 0) showOffset.value = 3;
                  refresh();
                }
              }}
              className="size-5"
            />
            <div className="flex items-center">
              <LikeIcon
                onClick={() => onLike(comment.commentId)}
                className={cn(
                  "size-5",
                  Boolean(comment.isLike) && "fill-main-red text-main-red"
                )}
              />
              <span className={cn(Boolean(comment.isLike) && "text-dim-red")}>
                {comment.likesCount}
              </span>
            </div>
          </div>
        </div>
      </div>
      {list.length > 0 && (
        <AnimatePresence>
          {list.map((reply) => (
            <CommentItem
              hadShownRedirectComment={hadShownRedirectComment}
              redirectCommentId={redirectCommentId}
              redirectCommentRootId={redirectCommentRootId}
              onRedirectedAction={onRedirectedAction}
              key={reply.commentId}
              comment={reply}
              isReply
              onLike={async (id) => {
                await onLike(id);
                refresh({
                  ...reply,
                  isLike: Number(!reply.isLike),
                  likesCount: reply.isLike
                    ? reply.likesCount - 1
                    : reply.likesCount + 1,
                });
              }}
              onReply={async (id, commentContent) => {
                await onReply(id, commentContent);
                refresh();
              }}
              onDelete={async (id) => {
                await onDelete(id);
                refresh(reply, true);
              }}
            />
          ))}
        </AnimatePresence>
      )}
      {!isReply &&
        Boolean(comment.repliesCount) &&
        !isExpandedSubComments.value && (
          <div
            className="text-dim-blue flex items-center text-xs"
            onClick={() => {
              isExpandedSubComments.value = true;
              if (showOffset.value === 0) showOffset.value = 3;
            }}
          >
            共{comment.repliesCount}条评论
            <DownArrowIcon className="size-5" />
          </div>
        )}
      {!isReply && calculatedHasMore && isExpandedSubComments.value && (
        <div
          className="text-dim-blue flex items-center pl-5 text-xs"
          onClick={loadMoreComments}
        >
          展示更多
          <DownArrowIcon className="size-5" />
        </div>
      )}
    </motion.div>
  );
};

// 主评论列表组件
export const GuideComments: FC<{
  objId: number;
  referenceId: string;
  onInputClickAction: () => Promise<unknown>;
  inputValue?: string;
  onLikeAction: (id: number) => Promise<unknown>;
  onReplyAction: (
    commentId: number,
    commentContent: string
  ) => Promise<unknown>;
  onDeleteAction: (id: number) => Promise<unknown>;
  hadShownRedirectComment: boolean;
  redirectCommentId?: string;
  redirectCommentRootId?: string;
  onRedirectedAction?: () => void;
}> = ({
  onInputClickAction,
  inputValue,
  onLikeAction,
  onReplyAction,
  onDeleteAction,
  objId,
  referenceId,
  hadShownRedirectComment,
  redirectCommentId,
  redirectCommentRootId,
  onRedirectedAction,
}) => {
  const { data, loadMore, refresh, hasMore, isLoading } = useCommentsList(
    referenceId
      ? {
          objId,
          objType: 100,
          pageSize: 50,
          referenceId,
        }
      : null
  );

  useEffect(() => {
    if (hadShownRedirectComment) return;
    if (
      !data?.list.find(
        (item) =>
          item.commentId === Number(redirectCommentRootId) ||
          item.commentId === Number(redirectCommentId)
      ) &&
      hasMore
    ) {
      loadMore();
      return;
    }
  }, [
    data?.list,
    hadShownRedirectComment,
    hasMore,
    loadMore,
    redirectCommentId,
    redirectCommentRootId,
  ]);

  return (
    <div className="flex h-full flex-col overflow-y-auto bg-white">
      <InfinityScroll
        className="divide-divider-2 flex-1 divide-y px-5"
        onLoadMore={loadMore}
        hasMore={hasMore}
        loading={isLoading}
      >
        <AnimatePresence>
          {data?.list.map((comment) => (
            <CommentItem
              hadShownRedirectComment={hadShownRedirectComment}
              redirectCommentId={redirectCommentId}
              redirectCommentRootId={redirectCommentRootId}
              onRedirectedAction={onRedirectedAction}
              objId={objId}
              key={comment.commentId}
              comment={comment}
              onLike={async (id) => {
                await onLikeAction(id);
                if (comment.commentId === id) {
                  refresh({
                    ...comment,
                    isLike: Number(!comment.isLike),
                    likesCount: comment.isLike
                      ? comment.likesCount - 1
                      : comment.likesCount + 1,
                  });
                }
              }}
              onReply={async (id, commentContent) => {
                await onReplyAction(id, commentContent);
                if (comment.commentId === id) {
                  refresh({
                    ...comment,
                    repliesCount: comment.repliesCount + 1,
                  });
                }
              }}
              onDelete={async (id) => {
                await onDeleteAction(id);
                if (comment.commentId === id) refresh(comment, true);
              }}
            />
          ))}
        </AnimatePresence>
      </InfinityScroll>
      <CommentInput
        inputValue={inputValue || ""}
        onClick={async () => {
          await onInputClickAction();
          refresh();
        }}
      />
    </div>
  );
};
