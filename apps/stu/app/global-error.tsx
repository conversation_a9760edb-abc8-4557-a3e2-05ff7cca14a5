/**
 * 全局错误兜底组件
 *
 * 功能特性:
 * 1. 捕获并展示详细的错误信息
 * 2. 解析堆栈信息并尝试映射到源码位置
 * 3. 支持source map解析 (需要完善)
 *
 * Source Map 完善建议:
 * 1. 安装专业库: npm install source-map-js
 * 2. 使用 SourceMapConsumer 进行精确的位置映射
 * 3. 处理 webpack 生成的 source map 格式
 * 4. 考虑缓存已解析的 source map 以提高性能
 */
"use client";

import { Button } from "@repo/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@repo/ui/components/card";
import { useEffect, useState } from "react";
import IcPageError from "@/public/icons/page-error.svg";

// Source map 解析相关类型
interface SourceMapPosition {
  source: string;
  line: number;
  column: number;
  name?: string;
}

interface ParsedStackFrame {
  functionName?: string;
  fileName: string;
  lineNumber: number;
  columnNumber: number;
  originalPosition?: SourceMapPosition;
}

interface GlobalErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

interface ErrorDetails {
  name: string;
  message: string;
  stack?: string;
  digest?: string;
  timestamp: string;
  userAgent: string;
  url: string;
  componentStack?: string;
  parsedStack?: ParsedStackFrame[];
}

export default function GlobalError({ error, reset }: GlobalErrorProps) {
  const [errorDetails, setErrorDetails] = useState<ErrorDetails | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [copied, setCopied] = useState(false);
  const [isParsingSourceMap, setIsParsingSourceMap] = useState(false);

  // 解析堆栈信息
  const parseStackTrace = (stack: string): ParsedStackFrame[] => {
    const frames: ParsedStackFrame[] = [];
    const lines = stack.split("\n");

    for (const line of lines) {
      // 匹配常见的堆栈格式
      // 例如: "at functionName (http://localhost:3000/_next/static/chunks/app/page-abc123.js:1:2345)"
      // 或者: "at http://localhost:3000/_next/static/chunks/app/page-abc123.js:1:2345"
      const match = line.match(/at\s+(?:(.+?)\s+\()?(.+?):(\d+):(\d+)\)?/);

      if (match) {
        const [, functionName, fileName, lineNumber, columnNumber] = match;
        if (fileName && lineNumber && columnNumber) {
          frames.push({
            functionName: functionName?.trim(),
            fileName: fileName.trim(),
            lineNumber: parseInt(lineNumber, 10),
            columnNumber: parseInt(columnNumber, 10),
          });
        }
      }
    }

    return frames;
  };

  // 尝试获取并解析source map
  const parseSourceMap = async (
    frames: ParsedStackFrame[]
  ): Promise<ParsedStackFrame[]> => {
    const enhancedFrames: ParsedStackFrame[] = [];

    for (const frame of frames) {
      const enhancedFrame = { ...frame };

      try {
        // 检查是否是压缩的JS文件
        if (
          frame.fileName.includes("/_next/static/") ||
          frame.fileName.includes(".min.js")
        ) {
          const sourceMapUrl = `${frame.fileName}.map`;

          // 尝试获取source map
          const response = await fetch(sourceMapUrl);
          if (response.ok) {
            const sourceMap = await response.json();

            // 注意: 这里是简化的source map解析
            // 生产环境建议使用专业库如 'source-map' 或 'source-map-js'
            // npm install source-map-js
            // 然后使用 SourceMapConsumer.with() 来正确解析位置映射
            if (sourceMap.sources && sourceMap.sources.length > 0) {
              // 简化处理：实际应该根据mappings字段进行精确映射
              const sourceIndex = Math.min(0, sourceMap.sources.length - 1);
              enhancedFrame.originalPosition = {
                source: sourceMap.sources[sourceIndex] || "未知源文件",
                line: frame.lineNumber, // 实际需要通过VLQ解码mappings
                column: frame.columnNumber,
                name: frame.functionName,
              };
            }
          }
        }
      } catch (err) {
        console.warn("解析source map失败:", err);
      }

      enhancedFrames.push(enhancedFrame);
    }

    return enhancedFrames;
  };

  useEffect(() => {
    // 收集错误详细信息
    const collectErrorDetails = async () => {
      const details: ErrorDetails = {
        name: error.name || "未知错误",
        message: error.message || "没有错误信息",
        stack: error.stack,
        digest: error.digest,
        timestamp: new Date().toISOString(),
        userAgent:
          typeof window !== "undefined" ? window.navigator.userAgent : "nextjs",
        url: typeof window !== "undefined" ? window.location.href : "nextjs",
      };

      // 如果有堆栈信息，尝试解析并增强
      if (error.stack) {
        try {
          setIsParsingSourceMap(true);
          const parsedFrames = parseStackTrace(error.stack);
          const enhancedFrames = await parseSourceMap(parsedFrames);
          details.parsedStack = enhancedFrames;
        } catch (err) {
          console.warn("解析堆栈信息失败:", err);
        } finally {
          setIsParsingSourceMap(false);
        }
      }

      setErrorDetails(details);
    };

    collectErrorDetails();

    // 记录错误到控制台
    console.error("全局捕获错误:", error);

    // 监听未捕获的错误和Promise拒绝
    const handleUnhandledError = (event: ErrorEvent) => {
      console.error("未处理的错误:", event.error);
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error("未处理的Promise拒绝:", event.reason);
    };

    window.addEventListener("error", handleUnhandledError);
    window.addEventListener("unhandledrejection", handleUnhandledRejection);

    return () => {
      window.removeEventListener("error", handleUnhandledError);
      window.removeEventListener(
        "unhandledrejection",
        handleUnhandledRejection
      );
    };
  }, [error]);

  const handleCopyError = async () => {
    if (!errorDetails) return;

    let parsedStackText = "";
    if (errorDetails.parsedStack && errorDetails.parsedStack.length > 0) {
      parsedStackText = "\n\n解析后的堆栈信息:\n";
      errorDetails.parsedStack.forEach((frame, index) => {
        parsedStackText += `${index + 1}. ${frame.functionName || "匿名函数"}\n`;
        parsedStackText += `   文件: ${frame.fileName}\n`;
        parsedStackText += `   位置: 第 ${frame.lineNumber} 行, 第 ${frame.columnNumber} 列\n`;
        if (frame.originalPosition) {
          parsedStackText += `   原始源码: ${frame.originalPosition.source} 第 ${frame.originalPosition.line} 行\n`;
        }
        parsedStackText += "\n";
      });
    }

    const errorText = `
错误详情:
名称: ${errorDetails.name}
消息: ${errorDetails.message}
时间: ${errorDetails.timestamp}
页面: ${errorDetails.url}
浏览器: ${errorDetails.userAgent}
${errorDetails.digest ? `错误摘要: ${errorDetails.digest}` : ""}${parsedStackText}${errorDetails.stack ? `\n原始堆栈信息:\n${errorDetails.stack}` : ""}
    `.trim();

    try {
      await navigator.clipboard.writeText(errorText);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("复制错误详情失败:", err);
    }
  };

  const handleReload = () => {
    window.location.reload();
  };

  const getErrorIcon = () => {
    if (
      error.message?.includes("ChunkLoadError") ||
      error.message?.includes("Loading chunk")
    ) {
      return "🔄";
    }
    if (
      error.message?.includes("Network") ||
      error.message?.includes("fetch")
    ) {
      return "🌐";
    }
    return "⚠️";
  };

  const getErrorTitle = () => {
    if (
      error.message?.includes("ChunkLoadError") ||
      error.message?.includes("Loading chunk")
    ) {
      return "应用需要更新";
    }
    if (
      error.message?.includes("Network") ||
      error.message?.includes("fetch")
    ) {
      return "网络连接异常";
    }
    return "应用发生错误";
  };

  const getErrorDescription = () => {
    if (
      error.message?.includes("ChunkLoadError") ||
      error.message?.includes("Loading chunk")
    ) {
      return "检测到新版本，请刷新页面以获取最新内容";
    }
    if (
      error.message?.includes("Network") ||
      error.message?.includes("fetch")
    ) {
      return "请检查网络连接后重试";
    }
    return "抱歉，应用遇到了意外错误，请尝试刷新页面";
  };

  return (
    <html>
      <body>
        <div className="flex min-h-screen items-center justify-center bg-sky-50">
          <div className="flex w-full flex-col items-center justify-center gap-2 p-4">
            <IcPageError className="size-20" />

            <div className="justify-start self-stretch text-center font-['Resource_Han_Rounded_SC'] text-xl font-bold leading-normal text-zinc-800/70">
              出错啦
            </div>

            <div
              className="inline-flex h-11 items-center justify-center gap-1 rounded-xl bg-white px-4 outline-1 outline-offset-[-1px] outline-zinc-800/10"
              onClick={handleReload}
            >
              <div className="h-4 w-16 justify-end text-center text-base font-medium leading-tight text-zinc-800/90">
                刷新
              </div>
            </div>

            <div className="justify-start self-stretch text-center font-['Resource_Han_Rounded_SC'] text-base font-normal leading-tight text-zinc-800/40">
              请尝试刷新页面
            </div>

            {/* 错误详情展开/收起 */}
            <div>
              <Button
                onClick={() => setShowDetails(!showDetails)}
                variant="ghost"
                className="w-full text-gray-600 hover:text-gray-800"
              >
                {showDetails ? "隐藏" : "显示"}错误详情
                <span className="ml-2">{showDetails ? "▲" : "▼"}</span>
              </Button>

              {showDetails && errorDetails && (
                <div className="mt-4 space-y-4">
                  {/* 基本错误信息 */}
                  <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                    <h3 className="mb-2 font-semibold text-red-800">
                      错误信息
                    </h3>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium text-red-700">类型:</span>
                        <span className="ml-2 text-red-600">
                          {errorDetails.name}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-red-700">消息:</span>
                        <span className="ml-2 text-red-600">
                          {errorDetails.message}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-red-700">时间:</span>
                        <span className="ml-2 text-red-600">
                          {new Date(errorDetails.timestamp).toLocaleString(
                            "zh-CN"
                          )}
                        </span>
                      </div>
                      {errorDetails.digest && (
                        <div>
                          <span className="font-medium text-red-700">
                            错误ID:
                          </span>
                          <span className="ml-2 font-mono text-xs text-red-600">
                            {errorDetails.digest}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 环境信息 */}
                  <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                    <h3 className="mb-2 font-semibold text-blue-800">
                      环境信息
                    </h3>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium text-blue-700">页面:</span>
                        <span className="ml-2 break-all text-blue-600">
                          {errorDetails.url}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-blue-700">
                          浏览器:
                        </span>
                        <span className="ml-2 text-xs text-blue-600">
                          {errorDetails.userAgent}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* 堆栈信息 */}
                  {(errorDetails.stack || errorDetails.parsedStack) && (
                    <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
                      <div className="mb-2 flex items-center justify-between">
                        <h3 className="font-semibold text-gray-800">
                          堆栈跟踪
                        </h3>
                        {isParsingSourceMap && (
                          <span className="text-xs text-blue-600">
                            正在解析源码位置...
                          </span>
                        )}
                      </div>

                      {/* 解析后的堆栈信息 */}
                      {errorDetails.parsedStack &&
                        errorDetails.parsedStack.length > 0 && (
                          <div className="mb-4">
                            <h4 className="mb-2 text-sm font-medium text-gray-700">
                              源码位置 (已解析)
                            </h4>
                            <div className="space-y-2">
                              {errorDetails.parsedStack
                                .slice(0, 5)
                                .map((frame, index) => (
                                  <div
                                    key={index}
                                    className="rounded border bg-white p-2 text-xs"
                                  >
                                    <div className="font-medium text-gray-800">
                                      {frame.functionName || "匿名函数"}
                                    </div>
                                    <div className="text-gray-600">
                                      文件: {frame.fileName.split("/").pop()}
                                    </div>
                                    <div className="text-gray-600">
                                      位置: 第 {frame.lineNumber} 行, 第{" "}
                                      {frame.columnNumber} 列
                                    </div>
                                    {frame.originalPosition && (
                                      <div className="mt-1 rounded bg-green-50 p-1 text-green-700">
                                        <div className="font-medium">
                                          原始源码:
                                        </div>
                                        <div>
                                          文件: {frame.originalPosition.source}
                                        </div>
                                        <div>
                                          位置: 第 {frame.originalPosition.line}{" "}
                                          行
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                ))}
                            </div>
                          </div>
                        )}

                      {/* 原始堆栈信息 */}
                      {errorDetails.stack && (
                        <div>
                          <h4 className="mb-2 text-sm font-medium text-gray-700">
                            原始堆栈信息
                          </h4>
                          <pre className="max-h-40 overflow-x-auto overflow-y-auto whitespace-pre-wrap rounded border bg-white p-3 text-xs text-gray-600">
                            {errorDetails.stack}
                          </pre>
                        </div>
                      )}
                    </div>
                  )}

                  {/* 复制按钮 */}
                  <div className="flex justify-center">
                    <Button
                      onClick={handleCopyError}
                      variant="outline"
                      className="text-sm"
                      disabled={copied}
                    >
                      {copied ? "已复制!" : "复制错误信息"}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
