/**
 * 评论模块 - Model 层
 *
 * 提供评论相关的数据获取和操作功能，包括：
 * - 获取评论列表
 * - 发布评论
 * - 删除评论
 * - 回复评论
 * - 点赞/取消点赞评论
 */

import { get, post } from "@/app/utils/fetcher";
import useSWR from "swr";
import useSWRInfinite from "swr/infinite";
import useSWRMutation from "swr/mutation";
import {
  ApiGetCommentsResponseSchema,
  ApiGetReferencesResponseSchema,
  type ApiGetCommentsResponse,
  type ApiGetReferencesResponse,
} from "./schemas";
import type {
  AddCommentPayload,
  CommentsListData,
  DeleteCommentPayload,
  GetCommentsParams,
  GetReferencesParams,
  GetSubCommentsParams,
  LikeCommentPayload,
  ReplyCommentPayload,
} from "./types";

const ApiPrefix = "/interaction-api";

// ==================== 数据获取 Hooks ====================

/**
 * 获取一级评论列表（支持无限滚动）
 */
export function useCommentsList(
  params: Omit<GetCommentsParams, "page" | "baseTime"> | null
) {
  const baseQueryParams = params
    ? {
        objId: params.objId.toString(),
        objType: params.objType.toString(),
        pageSize: params.pageSize.toString(),
        referenceId: params.referenceId,
      }
    : null;

  const { data, error, isLoading, mutate, size, setSize } =
    useSWRInfinite<ApiGetCommentsResponse>(
      (pageIndex: number) => {
        const queryParams = {
          ...baseQueryParams,
          page: (pageIndex + 1).toString(),
        };
        return baseQueryParams
          ? `/api/v1/comments/list?${new URLSearchParams(queryParams).toString()}`
          : null;
      },
      (url: string) =>
        get<ApiGetCommentsResponse>(`${url}&baseTime=${Date.now()}`, {
          prefix: ApiPrefix,
        }),
      {
        onSuccess: (data) => {
          // 使用 Zod 校验后端返回的数据
          return data.map((page) => ApiGetCommentsResponseSchema.parse(page));
        },
        revalidateOnFocus: false, // 切换标签或页面聚焦时，不自动刷新
        revalidateOnReconnect: false, // 网络恢复后，不自动刷新
        revalidateIfStale: false, // 初次挂载如果有旧数据，不自动刷新
        refreshInterval: 0, // 禁用轮询
        refreshWhenHidden: false,
        refreshWhenOffline: false,
        revalidateFirstPage: false,
      }
    );

  // 合并所有页面的数据
  const allComments = data ? data.flatMap((page) => page.list) : [];
  const pageInfo = data?.[0]?.pageInfo;
  const extraInfo = data?.[0]?.extraInfo;
  const totalPage = pageInfo?.totalPage ?? pageInfo?.totalPages ?? 0;

  return {
    data: data
      ? ({
          list: allComments,
          pageInfo: pageInfo!,
          extraInfo: extraInfo!,
        } as CommentsListData)
      : undefined,
    error,
    isLoading,
    refresh: (newItem?: CommentsListData["list"][number], isDelete = false) => {
      if (!newItem || !data) return mutate();
      const result = data.map((commentList) => ({
        ...commentList,
        list: [
          ...commentList.list
            .filter((item) =>
              isDelete ? item.commentId !== newItem.commentId : true
            )
            .map((item) =>
              item.commentId === newItem.commentId ? newItem : item
            ),
        ],
      }));
      return mutate(result, {
        optimisticData: result,
        populateCache: true,
        revalidate: true,
      });
    },
    loadMore: () => setSize(size + 1),
    hasMore: pageInfo ? size < totalPage : false,
    isLoadingMore: isLoading && data && data.length > 0,
  };
}

/**
 * 获取二级评论列表（支持无限滚动）
 */
export function useSubCommentsList(
  params: Omit<GetSubCommentsParams, "page"> | null
) {
  const baseQueryParams = params
    ? {
        objId: params.objId.toString(),
        objType: params.objType.toString(),
        pageSize: params.pageSize.toString(),
        commentRootId: params.commentRootId.toString(),
        ...(params.baseTime && { baseTime: params.baseTime.toString() }),
      }
    : null;

  const { data, error, isLoading, mutate, size, setSize } =
    useSWRInfinite<ApiGetCommentsResponse>(
      (pageIndex: number) => {
        const queryParams = {
          ...baseQueryParams,
          page: (pageIndex + 1).toString(),
        };
        return baseQueryParams
          ? `/api/v1/comments/sub-list?${new URLSearchParams(queryParams).toString()}`
          : null;
      },
      (url: string) => get<ApiGetCommentsResponse>(url, { prefix: ApiPrefix }),
      {
        onSuccess: (data: ApiGetCommentsResponse[]) => {
          // 使用 Zod 校验后端返回的数据
          return data.map((page) => ApiGetCommentsResponseSchema.parse(page));
        },
        revalidateOnFocus: false, // 切换标签或页面聚焦时，不自动刷新
        revalidateOnReconnect: false, // 网络恢复后，不自动刷新
        revalidateIfStale: false, // 初次挂载如果有旧数据，不自动刷新
        refreshInterval: 0, // 禁用轮询
        refreshWhenHidden: false,
        refreshWhenOffline: false,
        revalidateFirstPage: false,
      }
    );

  // 合并所有页面的数据
  const allComments = data
    ? data.flatMap((page: ApiGetCommentsResponse) => page.list)
    : [];
  const pageInfo = data?.[0]?.pageInfo;
  const extraInfo = data?.[0]?.extraInfo;
  const totalPage = pageInfo?.totalPage ?? pageInfo?.totalPages ?? 0;

  return {
    data: data
      ? ({
          list: allComments,
          pageInfo: pageInfo!,
          extraInfo: extraInfo!,
        } as CommentsListData)
      : undefined,
    error,
    isLoading,
    refresh: (newItem?: CommentsListData["list"][number], isDelete = false) => {
      if (!newItem || !data) return mutate();
      const result = data.map((commentList) => ({
        ...commentList,
        list: [
          ...commentList.list
            .filter((item) =>
              isDelete ? item.commentId !== newItem.commentId : true
            )
            .map((item) =>
              item.commentId === newItem.commentId ? newItem : item
            ),
        ],
      }));
      return mutate(result, {
        optimisticData: result,
        populateCache: true,
        revalidate: true,
      });
    },
    loadMore: () => setSize(size + 1),
    hasMore: pageInfo ? size < totalPage : false,
    isLoadingMore: isLoading && data && data.length > 0,
  };
}

/**
 * 获取引用列表（只加载前100条）
 */
export function useReferencesList(
  params: Omit<GetReferencesParams, "page" | "pageSize">
) {
  const { data, error, isLoading, mutate } = useSWR<ApiGetReferencesResponse>(
    `/api/v1/references/list`,
    (url: string) =>
      get<ApiGetReferencesResponse>(url, {
        query: {
          objId: params.objId.toString(),
          objType: params.objType.toString(),
          pageSize: "100", // 固定为100条
          page: "1", // 固定为第1页
        },
        prefix: ApiPrefix,
      }),
    {
      onSuccess: (data: ApiGetReferencesResponse) => {
        // 使用 Zod 校验后端返回的数据
        return ApiGetReferencesResponseSchema.parse(data);
      },
      revalidateOnFocus: false, // 切换标签或页面聚焦时，不自动刷新
      revalidateOnReconnect: false, // 网络恢复后，不自动刷新
      revalidateIfStale: false, // 初次挂载如果有旧数据，不自动刷新
      refreshInterval: 0, // 禁用轮询
      refreshWhenHidden: false,
      refreshWhenOffline: false,
    }
  );

  return {
    data: data?.list,
    error,
    isLoading,
    refresh: mutate,
  };
}

// ==================== 数据操作 Hooks ====================

/**
 * 发布评论
 */
export function useAddComment() {
  const { trigger, isMutating, error } = useSWRMutation(
    `/api/v1/comments/add`,
    async (url: string, { arg }: { arg: AddCommentPayload }) =>
      post(url, { arg, prefix: ApiPrefix })
  );

  return {
    addComment: trigger,
    isAdding: isMutating,
    error,
  };
}

/**
 * 删除评论
 */
export function useDeleteComment() {
  const { trigger, isMutating, error } = useSWRMutation(
    `/api/v1/comments/delete`,
    async (url: string, { arg }: { arg: DeleteCommentPayload }) =>
      post(url, { arg, prefix: ApiPrefix })
  );

  return {
    deleteComment: trigger,
    isDeleting: isMutating,
    error,
  };
}

/**
 * 回复评论
 */
export function useReplyComment() {
  const { trigger, isMutating, error } = useSWRMutation(
    `/api/v1/comments/reply`,
    async (url: string, { arg }: { arg: ReplyCommentPayload }) =>
      post(url, { arg, prefix: ApiPrefix })
  );

  return {
    replyComment: trigger,
    isReplying: isMutating,
    error,
  };
}

/**
 * 点赞评论
 */
export function useLikeComment() {
  const { trigger, isMutating, error } = useSWRMutation(
    `/api/v1/comments/like`,
    async (url: string, { arg }: { arg: LikeCommentPayload }) =>
      post(url, { arg, prefix: ApiPrefix })
  );

  return {
    likeComment: trigger,
    isLiking: isMutating,
    error,
  };
}
