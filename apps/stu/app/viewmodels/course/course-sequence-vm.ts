"use client";
import { useCourseModel } from "@/app/models/course-model";
import { LocalCourseProgress } from "@/app/models/local-progress";
import { finishLesson, surveillanceReport } from "@/app/utils/device";
import { post } from "@/app/utils/fetcher";
import {
  CourseWidget,
  CourseWidgetSummary,
  WidgetStatus,
} from "@/types/app/course";
import {
  batch,
  Signal,
  useSignal,
  useSignalEffect,
} from "@preact-signals/safe-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import useSWRMutation from "swr/mutation";

interface WidgetLoaderItem {
  key: string;
  data: CourseWidget;
}

type CourseSequenceViewmodel = {
  /**
   * 是否版本变更
   */
  isVersionChanged: Signal<boolean>;
  /**
   * 是否加载中
   */
  isLoading: boolean;
  /**
   * 知识点ID
   */
  knowledgeId: number;
  /**
   * 课程ID
   */
  lessonId: number;
  /**
   * 当前index
   */
  currentIndex: Signal<number>;
  /**
   * 当前组件
   */
  currentWidget: CourseWidgetSummary | undefined;
  /**
   * 组件摘要序列, 用于进度显示
   */
  widgetSummarySequence: CourseWidgetSummary[];
  /**
   * 总页数
   */
  total: number;
  /**
   * 按index跳转组件
   */
  goto: (index: number) => void;
  /**
   * 下一页
   */
  next: () => void;
  /**
   * 上一页
   */
  prev: () => void;
  /**
   * 错误信息
   */
  error: Error | undefined;
  /**
   * 报告进度
   */
  reportProgress: (params: {
    widgetIndex: number;
    costSeconds: number;
    status: "locked" | "completed";
  }) => Promise<void>;
  /**
   * 需上报的耗时
   */
  reportCostTime: (n: number) => void;
  /**
   * 本地进度记录器
   */
  localProgressRecorder: LocalCourseProgress;
  /**
   * 是否显示动画
   */
  showAnimation: Signal<boolean>;
  /**
   * 完成动画事件
   */
  completeAnimation: () => void;
  /**
   * 下一题参数
   */
  nextQuestionParams: string;
};

const useCourseSequenceViewmodel = (
  knowledgeId: number,
  redirectWidgetIndex?: string,
  knowledgePointName = ""
) => {
  // 获取课程信息
  const { data, isLoading, error } = useCourseModel(knowledgeId);
  const {
    lessonId = 0,
    total = 0,
    widgets = [],
    nextQuestionParams = "",
    currentWidgetIndex = 0,
    lessonVersion = "",
  } = data ?? {};

  const isVersionChanged = useSignal(false);
  const currentIndex = useSignal(
    redirectWidgetIndex ? Number(redirectWidgetIndex) : 0
  );
  const targetIndex = useSignal(0);
  const [widgetSummaries, setWidgetSummaries] = useState<CourseWidgetSummary[]>(
    []
  );
  const currentWidget = useMemo(() => {
    return widgetSummaries[currentIndex.value];
  }, [currentIndex.value, widgetSummaries]);
  const {
    trigger: doReportProgress,
    isMutating: isReporting,
    data: reportProgressData,
  } = useSWRMutation("/api/v1/lesson/progress/report", post);

  const widgetCostTime = useSignal(new Map<number, number>());
  const localProgressRecorder = useMemo(
    () => new LocalCourseProgress(knowledgeId, lessonId),
    [knowledgeId, lessonId]
  );

  const changeByUser = useSignal(false);
  const showAnimation = useSignal(false);

  const reportCostTime = useCallback(
    (n: number) => {
      widgetCostTime.value.set(currentIndex.value, n);
    },
    [currentIndex, widgetCostTime]
  );
  const reportProgress = useCallback(
    async ({
      widgetIndex,
      costSeconds,
      status,
    }: {
      widgetIndex: number;
      costSeconds: number;
      status: WidgetStatus;
    }) => {
      surveillanceReport({
        operationType: 4,
        learningProgress: (widgetIndex / widgetSummaries.length) * 100,
        studyTime: Array.from(widgetCostTime.value.values()).reduce(
          (acc, cur) => acc + cur,
          0
        ),
        knowledgePointId: knowledgeId,
        knowledgePointName,
      });
      await doReportProgress({
        knowledgeId,
        lessonId,
        widgetIndex,
        costSecond: costSeconds,
        status,
      });
    },
    [
      doReportProgress,
      knowledgeId,
      knowledgePointName,
      lessonId,
      widgetCostTime.value,
      widgetSummaries.length,
    ]
  );

  const goto = useCallback(
    (target: number) => {
      console.log("1 next::goto", target);
      if (isReporting) return;

      if (target < 0 || target === currentIndex.value) {
        return;
      }
      const current = currentIndex.value;
      // 下一个才上报
      if (target === current + 1) {
        reportProgress({
          widgetIndex: current,
          costSeconds: widgetCostTime.value.get(current) ?? 1,
          status: "completed",
        }).then(() => {
          console.log("next::reportProgress done");
        });
      }
      // 如果target大于total，则直接完成
      if (target >= total) {
        console.log("5 next::----THE END----");
        finishLesson(
          Array.from(widgetCostTime.value.values()).reduce(
            (acc, cur) => acc + cur,
            0
          )
        );
        return;
      }
      targetIndex.value = target;
      console.log("1-2 next::targetIndex", target);
      // 判断是否需要播放动效// 判断是否需要播放动效
      const currentWidget = widgetSummaries[current];
      const targetWidget = widgetSummaries[target];
      batch(() => {
        changeByUser.value = true;
        showAnimation.value = currentWidget?.type !== targetWidget?.type;
      });
    },
    [
      isReporting,
      total,
      currentIndex,
      targetIndex,
      showAnimation,
      widgetSummaries,
      changeByUser,
      reportProgress,
      widgetCostTime,
    ]
  );

  const completeAnimation = useCallback(() => {
    console.log(
      "4 next::completeAnimation",
      currentIndex.value,
      targetIndex.value
    );
    const target = targetIndex.value;

    // 更新widgetSummarySequence
    setWidgetSummaries((prev) => {
      const newSummaries = prev.map((it, index) => {
        if (index === currentIndex.value) {
          return { ...it, status: "completed" };
        }
        return it;
      });
      // console.log(newSummaries.map((it) => it.status));
      return newSummaries as CourseWidgetSummary[];
    });
    // 更新currentIndex
    currentIndex.value = target;
    console.log("5 next::----done----");
  }, [currentIndex, targetIndex.value]);

  const next = useCallback(() => {
    goto(currentIndex.value + 1);
  }, [goto, currentIndex]);

  const prev = useCallback(() => {
    goto(currentIndex.value - 1);
  }, [goto, currentIndex]);

  useEffect(() => {
    if (redirectWidgetIndex) return;
    currentIndex.value = currentWidgetIndex;
  }, [currentWidgetIndex, currentIndex, redirectWidgetIndex]);

  useEffect(() => {
    if (isLoading) return;
    if (!widgets || widgets.length === 0) return;
    if (widgetSummaries.length === 0) {
      setWidgetSummaries([...widgets]);
    }
    // setWidgetSummaries([...widgets]);
  }, [isLoading, widgets, widgetSummaries]);

  useEffect(() => {
    if (isReporting) return;
    if (reportProgressData) {
      // 判断版本是否更新
      isVersionChanged.value =
        lessonVersion !==
        (reportProgressData as { lessonVersion: string }).lessonVersion;
    }
  }, [isReporting, lessonVersion, reportProgressData, isVersionChanged]);

  useSignalEffect(() => {
    console.log(
      "3 next::useSignalEffect---",
      changeByUser.value,
      showAnimation.value
    );
    // 如果用户手动切换了index，并且没有动画，则等同于完成动画
    if (changeByUser.value && showAnimation.value === false) {
      completeAnimation();
    }
  });

  return {
    isVersionChanged,
    knowledgeId,
    lessonId,
    total,
    currentIndex,
    currentWidget,
    widgetSummarySequence: widgetSummaries,
    next,
    prev,
    goto,
    error: error as Error,
    reportProgress,
    reportCostTime,
    localProgressRecorder,
    showAnimation,
    completeAnimation,
    isLoading,
    nextQuestionParams,
  };
};

export {
  useCourseSequenceViewmodel,
  type CourseSequenceViewmodel,
  type WidgetLoaderItem,
};
