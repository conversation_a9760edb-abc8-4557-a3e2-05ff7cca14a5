"use client";

// import ScrollCorner from "@/public/images/corner.svg";
import { DialogView } from "@/app/components/dialog/default-dialog";
import { useCourseWidgetModel } from "@/app/models/course-widget-model";
import cornerLottie from "@/public/lottie/corner.json";
import { CourseWidgetSummary } from "@/types/app/course";
import { cn } from "@repo/ui/lib/utils";
import Lottie from "lottie-react";
import { ComponentProps, FC, useCallback, useMemo } from "react";
import { ExerciseInCourseView } from "../exercise-in-course";
import { GuideWidgetView } from "../guide/guide-view";
import { InteractiveView } from "../interactive/interactive-view";
import { VideoWidgetView } from "../video/video-view";
import { useCourseViewContext } from "./course-view-context";

const WidgetLoader: FC<
  {
    total: number;
    isActive: boolean;
    isNext: boolean;
    isPrev: boolean;
    summary: CourseWidgetSummary | undefined;
  } & ComponentProps<"div">
> = ({ total, isActive, isNext, isPrev, summary }) => {
  const {
    next,
    reportCostTime,
    knowledgeId,
    lessonId,
    nextQuestionParams,
    isExerciseCompleted,
  } = useCourseViewContext();

  const shouldLoad = useMemo(() => {
    return isActive || isNext || isPrev;
  }, [isActive, isNext, isPrev]);

  const { data, isLoading, error } = useCourseWidgetModel({
    knowledgeId,
    lessonId,
    summary: shouldLoad ? summary : undefined,
    nextQuestionParams,
  });

  const handleComplete = useCallback(
    (totalTimeSpent?: number) => {
      if (totalTimeSpent) {
        reportCostTime(totalTimeSpent);
      }
      if (!isExerciseCompleted.value?.[data?.index || 0]) {
        isExerciseCompleted.value[data?.index || 0] = true;
      }

      next();
    },
    [next, reportCostTime, data?.index, isExerciseCompleted]
  );

  const renderWidget = useCallback(() => {
    if (!data) return null;

    const { type } = data;
    if (type === "guide") {
      return (
        <GuideWidgetView
          content={data}
          active={isActive}
          totalGuideCount={total}
        />
      );
    }

    if (type === "exercise") {
      // 获取练习数据
      let exerciseData = data;
      if (isExerciseCompleted.value?.[data.index]) {
        exerciseData = {
          ...exerciseData,
          data: {
            ...exerciseData.data,
            hasNextQuestion: false,
          },
        };
      }

      return (
        <ExerciseInCourseView
          activeInCourse={isActive}
          widgetIndex={data.index}
          exerciseData={exerciseData}
          onComplete={handleComplete}
        />
      );
    }

    if (type === "interactive") {
      const interactiveData = data;
      return (
        <InteractiveView
          active={isActive}
          index={data.index}
          url={interactiveData.data.url}
          type={interactiveData.data.typeName}
          onReport={(e) => {
            console.log(e);
          }}
        />
      );
    }

    if (type === "video") {
      return (
        <VideoWidgetView
          content={data}
          active={isActive}
          totalGuideCount={total}
        />
      );
    }

    return (
      <div className="course-widget-unsupported">不支持的组件类型: {type}</div>
    );
  }, [data, total, isActive, handleComplete, isExerciseCompleted]);

  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center bg-white">
        <p className="text-center text-sm text-stone-700">加载中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full w-full items-center justify-center bg-white">
        <p className="text-red-1 text-center text-sm">{error.message}</p>
      </div>
    );
  }

  return (
    <div
      data-name={`widget-${summary?.name}`}
      className="relative h-screen w-full"
    >
      {renderWidget()}
    </div>
  );
};

const LottieCorner: FC<{
  show: boolean;
  className?: string;
  onComplete?: () => void;
}> = ({ show, className, onComplete }) => {
  if (!show) {
    return null;
  }
  return (
    <Lottie
      className={className}
      animationData={cornerLottie}
      autoPlay={true}
      loop={false}
      onComplete={() => {
        onComplete?.();
      }}
    />
  );
};

const CourseWidgetsLoaderView: FC<ComponentProps<"div">> = () => {
  const {
    isVersionChanged,
    isLoading,
    error,
    currentIndex,
    total,
    widgetSummarySequence,
    showAnimation,
    refCourseContainer,
    exit,
  } = useCourseViewContext();

  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center justify-center bg-white">
        <p className="text-center text-sm text-stone-700">加载中...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full w-full items-center justify-center bg-white">
        <p className="text-red-1 text-center text-sm">{error.message}</p>
      </div>
    );
  }

  return (
    <>
      <LottieCorner
        show={showAnimation.value}
        className="z-100 fixed left-0 top-0 w-full bg-transparent"
        onComplete={() => (showAnimation.value = false)}
      />
      <div
        ref={refCourseContainer}
        className="relative h-screen w-full transform-gpu overflow-y-scroll"
      >
        <DialogView
          title="当前课程内容已更新，请退出后重新进入课程"
          open={isVersionChanged.value}
          buttons={[
            {
              text: "确定",
              onClick: exit,
              color: "red",
            },
          ]}
        ></DialogView>

        {widgetSummarySequence.map((summary) => {
          const { index } = summary;
          return (
            <div
              data-name={`${index}`}
              key={`widget-${index}`}
              className={cn(
                "relative h-screen w-full overflow-y-auto overscroll-none",
                summary.type === "video" && "bg-black"
              )}
            >
              <div className="h-0.25 invisible" />
              <WidgetLoader
                total={total}
                isActive={index === currentIndex.value}
                isNext={index === currentIndex.value + 1}
                isPrev={index === currentIndex.value - 1}
                summary={summary}
              />
            </div>
          );
        })}
      </div>
    </>
  );
};

export { CourseWidgetsLoaderView };
