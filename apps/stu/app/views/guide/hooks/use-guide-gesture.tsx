import { batch, useSignal } from "@preact-signals/safe-react";
import { RefObject, useEffect } from "react";
import { useThrottledCallback } from "use-debounce";

export const useGuideGesture = (
  ref: RefObject<HTMLDivElement | null>,
  onPan: () => void
) => {
  const isTriggerByUser = useSignal(false);
  const scrollTop = useSignal(0);
  const scrollTopOffset = useSignal(0);
  const hasTriggerOnPan = useSignal(false);

  const container = ref.current;

  const throttledScroll = useThrottledCallback(() => {
    if (!container) return;

    scrollTopOffset.value = container.scrollTop - scrollTop.value;
    // 先处理原有onPan逻辑
    if (
      hasTriggerOnPan.value === false &&
      isTriggerByUser.value === true &&
      Math.abs(scrollTopOffset.value) > 120
    ) {
      onPan();
      hasTriggerOnPan.value = true;
    }
  }, 40);

  useEffect(() => {
    if (!container) {
      return;
    }
    const handleUserAction = () => {
      if (isTriggerByUser.value === true) return;
      isTriggerByUser.value = true;
      scrollTop.value = container.scrollTop;
    };
    const handleTouchEnd = () => {
      batch(() => {
        scrollTop.value = container.scrollTop;
        scrollTopOffset.value = 0;
        isTriggerByUser.value = false;
        hasTriggerOnPan.value = false;
      });
    };

    container.addEventListener("wheel", handleUserAction);
    container.addEventListener("touchstart", handleUserAction);
    container.addEventListener("touchmove", throttledScroll);
    container.addEventListener("touchend", handleTouchEnd);
    container.addEventListener("scroll", throttledScroll);
    return () => {
      container.removeEventListener("touchstart", handleUserAction);
      container.removeEventListener("wheel", handleUserAction);
      container.removeEventListener("touchend", handleTouchEnd);
      container.removeEventListener("touchmove", throttledScroll);
      container.removeEventListener("scroll", throttledScroll);
    };
  }, [
    container,
    onPan,
    throttledScroll,
    scrollTop,
    scrollTopOffset,
    isTriggerByUser,
    hasTriggerOnPan,
  ]);
};
