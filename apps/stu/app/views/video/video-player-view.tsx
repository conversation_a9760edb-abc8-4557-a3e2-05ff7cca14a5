import { TranslucentGlassButton } from "@/app/components/guide/guide-buttons";
import { useSignal } from "@preact-signals/safe-react";
import { Player } from "@remotion/player";
import { cn } from "@repo/ui/lib/utils";
import { FC, useCallback } from "react";
import { AbsoluteFill, Video } from "remotion";
import { useDoubleTap } from "use-double-tap";
import { LongPressEventType, useLongPress } from "use-long-press";
import { useVideoViewContext } from "./video-view-context";

const Inner: FC<{ url: string }> = ({ url }) => {
  const errMsg = useSignal("");

  if (errMsg.value) {
    return (
      <div className="flex h-full w-full items-center justify-center bg-white">
        <p className="text-red-1 text-center text-sm">{errMsg.value}</p>
      </div>
    );
  }
  return (
    <AbsoluteFill className="flex items-center justify-center bg-black/20">
      <Video
        src={url}
        pauseWhenBuffering
        crossOrigin="anonymous"
        onError={(e) => {
          errMsg.value = e.message;
          return;
        }}
      />
    </AbsoluteFill>
  );
};

export const VideoPlayerView: FC<{ className?: string }> = ({ className }) => {
  const {
    data,
    refPlayer,
    playRate,
    fps,
    durationInFrames,
    set3XPlayRate,
    resetPlayRate,
    togglePlayerControls,
    togglePlay,
    next,
  } = useVideoViewContext();
  const { url, duration } = data;

  const longPressHandlers = useLongPress(set3XPlayRate, {
    onFinish: () => {
      resetPlayRate();
      // trackEventWithLessonId("doc_fast_forward_longpress");
    },
    // onCancel: () => {
    //   console.log("onCancel");
    //   resetPlayRate();
    // },
    detect: LongPressEventType.Touch,
  });
  const handleClick = useCallback(() => {
    togglePlayerControls();
  }, [togglePlayerControls]);

  const handleDoubleClick = useCallback(() => {
    togglePlay();
    // trackEventWithLessonId("doc_play_pause_doubleclick");
  }, [togglePlay]);

  const doubleTapHandlers = useDoubleTap(handleDoubleClick, 300, {
    onSingleTap: handleClick,
  });

  if (duration === 0) {
    return (
      <div className="flex h-full w-full items-center justify-center bg-white">
        <p className="text-red-1 text-center text-sm">
          缺少视频时长，需在后台重新上传视频
        </p>
        <TranslucentGlassButton className="w-18 h-12" onClick={next}>
          跳过
        </TranslucentGlassButton>
      </div>
    );
  }

  return (
    <div
      data-name="video-player"
      className="h-screen w-full bg-black"
      {...doubleTapHandlers}
      {...longPressHandlers()}
    >
      <Player
        ref={refPlayer}
        className={cn("h-full w-full", className)}
        style={{ width: "100%" }}
        component={Inner}
        inputProps={{ url }}
        playbackRate={playRate.value}
        fps={fps}
        durationInFrames={durationInFrames}
        // autoPlay
        // controls
        // alwaysShowControls
        allowFullscreen={false}
        // compositionWidth={screen.width}
        // compositionHeight={screen.height}
        compositionWidth={1920}
        compositionHeight={1080}
        acknowledgeRemotionLicense
        errorFallback={(e: { error: { message: string } }) => (
          <span className="text-sm text-red-500">错误: {e.error.message}</span>
        )}
      />
    </div>
  );
};
