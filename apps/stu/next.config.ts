import analyzer from "@next/bundle-analyzer";
import { withSentryConfig } from "@sentry/nextjs";
import type { NextConfig } from "next";
import path from "path";
const withBundleAnalyzer = analyzer({
  enabled: process.env.NEXT_PUBLIC_ANALYZE === "true",
});

//    import { PHASE_PRODUCTION_BUILD } from "next/dist/shared/lib/constants";

const nextConfig: NextConfig = withBundleAnalyzer({
  productionBrowserSourceMaps: true,
  distDir: "dist",
  output: "standalone",
  assetPrefix:
    process.env.NODE_ENV === "production"
      ? "https://static.xiaoluxue.com/stu"
      : process.env.NODE_ENV === "test"
        ? "https://static.test.xiaoluxue.cn/stu"
        : "",
  transpilePackages: [
    "@repo/ui",
    "@repo/lib",
    "@repo/core",
    "@repo/rough-notation",
  ],
  experimental: {
    swcPlugins: [
      [
        "@preact-signals/safe-react/swc",
        {
          // you should use `auto` mode to track only components which uses `.value` access.
          // Can be useful to avoid tracking of server side components
          mode: "auto",
        } /* plugin options here */,
      ],
    ],
  },
  turbopack: {
    rules: {
      "*.svg": {
        loaders: [
          {
            loader: "@svgr/webpack",
            options: {
              icon: false,
            },
          },
        ],
        as: "*.js",
      },
    },
  },
  // 配置重写规则
  rewrites: async () => {
    return [
      {
        source: "/api/:path*",
        // destination: `https://m1.apifoxmock.com/m1/6405749-6102927-default/api/:path*`,
        destination: `/api/:path*`,
      },
    ];
  },
  outputFileTracingRoot: path.resolve(__dirname, "../../"),
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**.aliyuncs.com",
        port: "",
        search: "",
      },
      {
        protocol: "https",
        hostname: "**.xiaoluxue.cn",
        port: "",
        search: "",
      },
      {
        protocol: "https",
        hostname: "**.xiaoluxue.com",
        port: "",
        search: "",
      },
    ],
  },
  webpack(config, { isServer }) {
    config.module.rules.push({
      test: /\.svg$/,
      use: [
        {
          loader: "@svgr/webpack",
          options: {
            icon: false,
          },
        },
      ],
    });
    config.optimization.splitChunks = {
      chunks: "all",
      minSize: 200 * 1024, // 设置最小包大小为 200KB
      cacheGroups: {
        default: false,
        vendors: false,
        common: {
          name: "common",
          minChunks: 2,
          priority: 10,
          reuseExistingChunk: true,
        },
      },
    };
    if (!isServer) {
      config.module.rules.push({
        test: /\.(woff2?|ttf|eot)$/,
        issuer: /\.(css|scss)$/,
        type: "asset/resource",
        generator: {
          filename: "static/fonts/[name][ext]",
        },
      });
    }
    return config;
  },
});

export default withSentryConfig(nextConfig, {
  // For all available options, see:
  // https://www.npmjs.com/package/@sentry/webpack-plugin#options

  org: "sentry",
  project: "stu",
  sentryUrl: "https://sentry.xiaoluxue.cn/",

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Uncomment to route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
  // side errors will fail.
  // tunnelRoute: "/monitoring",

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: true,
  sourcemaps: {
    disable: false,
    assets: ["**/*.js", "**/*.js.map"],
    ignore: ["**/node_modules/**"],
    deleteSourcemapsAfterUpload: true,
  },
});
