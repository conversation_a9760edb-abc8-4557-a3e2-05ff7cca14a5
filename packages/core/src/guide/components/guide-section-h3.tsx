import { useSignal } from "@preact-signals/safe-react";
import { useEffect, useRef } from "@preact-signals/safe-react/react";
import { FC } from "react";
import { useCurrentFrame } from "remotion";
import {
  SectionH3Provider,
  SectionH3ProviderProps,
  useSectionH3Context,
} from "../context/section-h3-context";
import { useSketchCanvasRef } from "../context/sketch-canvas-context";
import { HandDrawn } from "./guide-hand-drawn";
import { GuideSectionH4 } from "./guide-section-h4";
import { SketchBoard } from "./sketch-board";

const Draw = () => {
  const { h3Line, sketchProps, partFrames } = useSectionH3Context();
  const { onDrawChange, eraserMode, highlighter } = sketchProps || {};
  const { canvasRef, strokeColor, strokeWidth } = useSketchCanvasRef();
  const frame = useCurrentFrame();
  if (!h3Line) {
    return null;
  }

  const { id, draw } = h3Line;

  if (sketchProps?.mode !== "draw" && draw) {
    return <HandDrawn data={draw} frame={frame} partFrames={partFrames} />;
  }

  if (sketchProps?.mode === "draw") {
    return (
      <SketchBoard
        startFrame={sketchProps?.startFrame || 0}
        outFrame={sketchProps?.outFrame || 0}
        itemId={id ?? ""}
        className="z-100 absolute left-0 top-0 h-full w-full"
        svgData={""}
        // readOnly={isReadOnly}
        onChange={onDrawChange}
        ref={canvasRef}
        strokeColor={strokeColor}
        strokeWidth={strokeWidth}
        allPaths={draw || []}
        animated={true}
        eraserMode={eraserMode}
        highlighter={highlighter}
      />
    );
  }
};

const SectionH3: FC = () => {
  const { ref, parts, mergedReferences } = useSectionH3Context();
  const svgRef = useRef<SVGSVGElement>(null);
  const scale = useSignal(1);
  const pathList = useSignal<
    {
      left: number;
      top: number;
      height: { value: number };
      width: number;
    }[]
  >([]);
  const countList = useSignal<
    {
      left: number;
      top: number;
      height: { value: number };
      width: number;
      count: number;
    }[]
  >([]);

  useEffect(() => {
    if (!ref.current || !svgRef.current) return;
    const rect = svgRef.current.getBoundingClientRect();
    scale.value = rect.width / 100;
    const doms = ref.current.querySelectorAll<
      HTMLImageElement | HTMLSpanElement
    >("[data-underline=true]");
    const paths: {
      left: number;
      top: number;
      height: { value: number };
      width: number;
    }[] = [];
    const counts: {
      left: number;
      top: number;
      height: { value: number };
      width: number;
      count: number;
    }[] = [];
    let height = { value: 0 };
    doms.forEach((dom) => {
      const lastDisplay = dom.style.display;
      if (dom.classList.contains("math-content-formula")) {
        dom.style.display = "inline-block";
      }
      const domRect = dom.getBoundingClientRect();
      if (dom.classList.contains("math-content-formula")) {
        dom.style.display = lastDisplay;
      }
      const last = paths.at(-1);
      if (!last) {
        height = { value: domRect.height };
        paths.push({
          left: domRect.left - rect.left,
          top: domRect.top - rect.top,
          height,
          width: domRect.width,
        });
      } else {
        if (domRect.top - rect.top > last.top + last.height.value) {
          height = { value: domRect.height };
          paths.push({
            width: domRect.width,
            height,
            left: domRect.left - rect.left,
            top: domRect.top - rect.top,
          });
        } else {
          if (domRect.left - rect.left <= last.left + last.width + 5) {
            last.width += domRect.width;
            last.height.value = Math.max(last.height.value, domRect.height);
          } else {
            paths.push({
              width: domRect.width,
              height,
              left: domRect.left - rect.left,
              top: domRect.top - rect.top,
            });
          }
        }
      }
      const count = Number(dom.dataset.lastCount);
      if (count) {
        counts.push({
          left: domRect.left - rect.left,
          top: domRect.top - rect.top,
          height,
          width: String(count).length * 8,
          count,
        });
      }
    });
    pathList.value = paths;
    countList.value = counts;
  }, [countList, mergedReferences, pathList, ref, scale, svgRef]);

  return (
    <section
      data-name="section::h3"
      ref={ref}
      className="relative flex flex-col gap-6"
    >
      {parts.map((part, index) => (
        <GuideSectionH4 key={index} data={part} />
      ))}

      <Draw />
      <svg
        className="pointer-events-none absolute left-0 top-0"
        ref={svgRef}
        xmlns="http://www.w3.org/2000/svg"
        width="100"
        height="2"
        overflow="visible"
        fill="none"
      >
        {pathList.value.map((rect, index) => (
          <path
            key={index}
            d={`M${rect.left} ${rect.top + rect.height.value / scale.value}h${rect.width / scale.value}`}
            stroke="#33302D"
            strokeOpacity="0.4"
            strokeWidth="2"
            strokeDasharray="2 4"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        ))}
        {countList.value.map((rect, index) => (
          <g
            transform={`translate(${rect.left + (String(rect.count).length * 8 + 8) / scale.value}, ${rect.top + rect.height.value / scale.value - 7 / scale.value})`}
            key={index}
          >
            <rect
              x="0"
              y="0"
              width={(String(rect.count).length * 8 + 8) / scale.value}
              height={14 / scale.value}
              rx="4"
              ry="4"
              fill="#797776"
            />
            <text
              x={(String(rect.count).length * 8 + 8) / scale.value / 2}
              y={8 / scale.value}
              fontSize="8"
              dominantBaseline="middle"
              textAnchor="middle"
              fill="#FFFFFF"
            >
              {rect.count}
            </text>
          </g>
        ))}
      </svg>
    </section>
  );
};

export const GuideSectionH3: FC<Omit<SectionH3ProviderProps, "children">> = (
  props
) => {
  return (
    <SectionH3Provider {...props}>
      <SectionH3 />
    </SectionH3Provider>
  );
};
