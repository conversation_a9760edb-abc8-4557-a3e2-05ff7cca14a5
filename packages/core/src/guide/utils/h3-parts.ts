import { Line } from "@repo/core/types/data/widget-guide";
import { v4 as uuidv4 } from "uuid";

const toH3Parts = (lines: Line[]) => {
  // console.log(lines);
  const parts: Line[][] = [];

  let currentGroup: Line[] = [];
  let order = 0;

  lines.forEach((line) => {
    if (!line.id) {
      line.id = uuidv4();
    }
    // 如果遇到新的h3，先保存前一组数据
    if (line.tag === "h3") {
      if (currentGroup.length > 0) {
        parts.push(currentGroup);
      }
      // 开始新的分组
      currentGroup = [line]; // h3本身也包含在分组中
      order = 0;
      return;
    }

    // 计算block行, 它的子ol增加的order
    if (line.tag === "block") {
      line.content.forEach((item) => {
        if (item.tag === "h4") {
          order = 0;
        }
        if (item.tag === "ol") {
          // 如果order不等于当前order，则设置order
          if (item.order !== order) {
            item.order = order;
          }
          order = order + 1;
        }
      });
    }
    // 如果遇到h4，重置order
    if (line.tag === "h4") {
      order = 0;
    }
    // 计算ol行增加order
    if (line.tag === "ol") {
      // 如果order不等于当前order，则设置order
      if (line.order !== order) {
        line.order = order;
      }
      order = order + 1;
    }
    // 将非h3的行添加到当前分组
    currentGroup.push(line);
  });

  // 处理最后一个分组
  if (currentGroup.length > 0) {
    parts.push(currentGroup);
  }
  // console.log([...parts]);
  return parts;
};

const toH4Parts = (lines: Line[]) => {
  // h3不参与分组, 只在分组后将h3添加到第一个part
  const [h3Line, ...rest] = lines;
  const parts: Line[][] = [];

  let currentGroup: Line[] = [];

  rest.forEach((line) => {
    // 如果遇到新的h4，先保存前一组数据
    // block等同h4
    if (line.tag === "h4" || line.tag === "block") {
      if (currentGroup.length > 0) {
        parts.push(currentGroup);
      }
      // 开始新的分组
      currentGroup = [line]; // h4本身也包含在分组中
      return;
    }

    // 将行添加到当前分组
    currentGroup.push(line);
  });

  // 处理最后一个分组
  if (currentGroup.length > 0) {
    parts.push(currentGroup);
  }
  if (h3Line && parts[0]) {
    parts[0].unshift(h3Line);
  }
  return parts;
};

export { toH3Parts, toH4Parts };
